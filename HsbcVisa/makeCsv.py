import re
from datetime import datetime
import glob

for txtFilename in sorted(glob.glob("*.txt")):

    file = open(txtFilename, encoding="utf-8")
    lines = file.readlines()
    file.close()

    file = open(txtFilename.split(".")[0] + ".csv", "w")
    for line in lines:
            line = line.strip()
            matches = re.match(r"(\d{1,2}/\d{1,2}/\d{2}) +(1887|5612) +([^ ]+( [^ ]+)*) +(-?\$[\d,]+\.\d{2})", line)
            if matches:
                date = datetime.strptime(matches.group(1), "%d/%m/%y").strftime("%Y-%m-%d")
                amount = matches.group(5).replace("$", "").replace(",", "")
                file.write(f"{date},{matches.group(2)},{matches.group(3)},{amount},0.5\n")
    file.close()
