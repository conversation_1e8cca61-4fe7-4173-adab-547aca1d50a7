use ShingMoney

CREATE TABLE HsbcAcct (
	ID int not null identity primary key,
	[Date] date NOT NULL,
	[Acct] varchar(20) not null,
	Description varchar(100) NOT NULL,
	Amount money not null,
	Shing decimal(10,4) not null,
	Source varchar(50) not null,
	InsertTime datetime not null default getdate()
)
GO

CREATE TABLE HsbcVisa (
	ID int not null identity primary key,
	[Date] date NOT NULL,
	[Card] char(4) not null,
	Merchant varchar(50) NOT NULL,
	Amount money not null,
	Shing decimal(10,4) not null,
	Source varchar(50) not null,
	InsertTime datetime not null default getdate()
)
GO

CREATE TABLE RoddiPaid (
	ID int not null identity primary key,
	[Date] date NOT NULL,
	Amount money not null,
	Comment varchar(200) not null,
	Shing decimal(10,4) not null,
	InsertTime datetime not null default getdate()
)
GO

CREATE TABLE ShingMoney (
	ID int not null identity primary key,
	[Date] date NOT NULL,
	Amount money not null,
	Comment varchar(200) not null,
	InsertTime datetime not null default getdate()
)
GO

CREATE TABLE ShingPaid (
	ID int not null identity primary key,
	[Date] date NOT NULL,
	Amount money not null,
	Comment varchar(200) not null,
	Roddi decimal(10,4) not null,
	InsertTime datetime not null default getdate()
)
GO
