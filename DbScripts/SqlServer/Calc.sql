use <PERSON>g<PERSON><PERSON>

declare @shingMoney money
select @shingMoney = sum(amount) from <PERSON><PERSON><PERSON><PERSON>

declare @shingPaid money
select @shingPaid = sum(amount*roddi) from ShingPaid

declare @hsbcAcct money
select @hsbcAcct = sum(amount*shing) from HsbcAcct

declare @hsbcVisa money
select @hsbcVisa = sum(amount*shing) from HsbcVisa

declare @roddiPaid money
select @roddiPaid = sum(amount*shing) from RoddiPaid

select @shingMoney as shingMoney
	,@shingPaid as shingPaid
	,@hsbcAcct as hsbcAcct
	,@hsbcVisa as hsbcVisa
	,@roddiPaid as roddiPaid
	,@shingMoney + @shingPaid - @hsbcAcct - @hsbcVisa - @roddiPaid as total
	,@shingMoney + @shingPaid - @hsbcAcct - @hsbcVisa - @roddiPaid + 34618 as totalWithoutLoans