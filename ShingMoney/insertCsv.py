import csv
import glob
import pyodbc 

conn = pyodbc.connect('DRIVER={ODBC Driver 18 for SQL Server};SERVER=localhost;DATABASE=ShingMoney;ENCRYPT=No;Trusted_Connection=Yes')
cursor = conn.cursor()

cursor.execute("delete from ShingMoney")

for csvFilename in sorted(glob.glob("*.csv")):
    with open(csvFilename) as csvFile:
        csvReader = csv.reader(csvFile, delimiter=',')
        for row in csvReader:
            cursor.execute(
                """INSERT INTO [dbo].[ShingMoney] ([Date],[Amount],[Comment])
                VALUES (?,?,?)""",
            row[0], row[1], row[2])
            print(row)

conn.commit()
