import re
from datetime import datetime as dt
import glob

def getShing(description):
    description = description.lower()
    if re.search(r"^lp.+shing", description):
        return 1
    if re.search(r"transfer|payment|martinewaynedc|mcdonalds", description):
        return 0
    return 0.5

def skip(description):
    description = description.lower()
    if  re.match(r"\d{2}(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)\d{2} ([^ ]+ ){1,2}\d{2}:\d{2}:\d{2}", description):
        return True
    if re.match(r"2% cashback$", description):
        return True
    return False

for txtFilename in sorted(glob.glob("*-EVERYDAY.txt")):

    file = open(txtFilename, encoding="utf-8")
    lines = file.readlines()
    file.close()

    file = open(txtFilename.split(".")[0] + ".csv", "w")

    date = None
    year = re.search(r"\d{4}", txtFilename).group(0)
    for line in lines:
        line = line.strip()

        matches = re.search(r"([0123]\d (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) +)?([^ ]+( [^ ]+)*( +AU)?) +([\d,]+\.\d{2})( +([\d,]+\.\d{2}))?$", line)
        if matches:
            if matches.group(1):
                date = dt.strptime(f"{matches.group(1).strip()} {year}", "%d %b %Y").strftime("%Y-%m-%d")
            description = matches.group(3).replace(",", " ")
            amount =  matches.group(6).replace(",", "")
            shing = getShing(description)
            if not skip(description):
                file.write(f"{date},EVERYDAY,{description},{amount},{shing}\n")

    file.close()
