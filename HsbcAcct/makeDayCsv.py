import re
from datetime import datetime as dt
import glob

def getShing(description):
    description = description.lower()

    if re.search(r"^lp.+shing", description):
        return 1

    if re.search(r"^transfer|payment|rtp|24009773 aust unity d/dbt|adyen|arliana|ebay commerce au|mcare benefits|hsbc credit ?card", description):
        return 0

    return 0.5

for txtFilename in sorted(glob.glob("*-DAY.txt")):

    file = open(txtFilename, encoding="utf-8")
    lines = file.readlines()
    file.close()

    file = open(txtFilename.split(".")[0] + ".csv", "w")

    date = None
    year = re.search(r"\d{4}", txtFilename).group(0)
    for line in lines:
        line = line.strip()
        matches = re.search(r"([0123]\d (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) +)?([^ ]+( [^ ]+)*) +([\d,]+\.\d{2})( +([\d,]+\.\d{2}))?$", line)
        if matches:
            if matches.group(1):
                date = dt.strptime(f"{matches.group(1).strip()} {year}", "%d %b %Y").strftime("%Y-%m-%d")
            description = matches.group(3).replace(",", " ")
            amount =  matches.group(5).replace(",", "")
            shing = getShing(description)
            file.write(f"{date},DAY,{description},{amount},{shing}\n")

    file.close()
