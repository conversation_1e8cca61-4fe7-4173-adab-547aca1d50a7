import os
import glob
import re

for pdfFilename in sorted(glob.glob("*.pdf")):

    pdfText = os.popen(f"pdftotext -layout {pdfFilename} -").read()

    acctName = ""
    acctLines = []
    acctStarted = False
    for line in pdfText.splitlines():
        line = line.strip()

        matches = re.match(r"(DAY TO DAY ACCOUNT|EVERYDAY GLOBAL A/C) +BSB No\. +(\d+) +Account +(\d+) +Currency +AUD", line)
        if matches:
            acctName = matches.group(1).split(" ")[0]
            acctStarted = True
            acctLines = []
        elif acctStarted and re.match(r"Transaction Total +[\d,]+\.\d{2} +[\d,]+\.\d{2}", line):
            acctStarted = False
            file = open(pdfFilename.split(".")[0] + "-" + acctName + ".txt", "w")
            file.write("\r\n".join(acctLines) + "\r\n")
            file.close()
        elif acctStarted and (
            re.match(r"[0123]\d (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) +", line)
            or re.search(" +[\d,]+\.\d{2}$", line)
            ) and (
            re.search(r"balance brought forward|closing balance", line.lower()) == None
            ):
            acctLines.append(line)



