using Dapper;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Data.SqlClient;

namespace Api.Handlers;

public class LoadHsbcAcctHandler
{
	public ActionResult<LoadHsbcAcctResponse> Execute(LoadHsbcAcctRequest request)
	{
		using var connection = new SqlConnection(AppConfig.ConnectionString);
		connection.Open();

		int one = connection.Execute("delete from HsbcAcct");

		var fileNames = Directory.GetFiles(AppConfig.HsbcAcctDir, "*.csv").Order();
		foreach (string filename in fileNames)
		{
			var lines = File.ReadAllLines(filename);

			var records = lines
				.Where(line => !string.IsNullOrWhiteSpace(line))
				.Select(line =>
				{
					var words = line.Split(',');
					return new
					{
						Date = DateTime.ParseExact(words[0], "yyyy-MM-dd", null),
						Acct = words[1],
						Description = words[2],
						Amount = decimal.Parse(words[3]),
						Shing = decimal.Parse(words[4]),
						Source = Path.GetFileName(filename)
					};
				});
				
			connection.Execute("insert into HsbcAcct (Date, Acct, Description, Amount, Shing, Source) values (@Date, @Acct, @Description, @Amount, @Shing, @Source)", records);
		}

		return new OkObjectResult(new LoadHsbcAcctResponse { Filenames = fileNames });
	}
}

public class LoadHsbcAcctRequest
{
}

public class LoadHsbcAcctResponse
{
	public required IEnumerable<string> Filenames { get; set; }
}