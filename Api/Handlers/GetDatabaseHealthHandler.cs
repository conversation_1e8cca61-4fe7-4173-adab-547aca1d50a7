using Dapper;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Data.SqlClient;

namespace Api.Handlers;

public class GetDatabaseHealthHandler
{
	public ActionResult<GetDatabaseHealthResponse> Execute(GetDatabaseHealthRequest request)
	{
		using var connection = new SqlConnection(AppConfig.ConnectionString);
		connection.Open();

		int one = connection.ExecuteScalar<int>("select 1");

		return new OkObjectResult(new GetDatabaseHealthResponse { IsHealthy = one == 1 });
	}
}

public class GetDatabaseHealthRequest
{
}

public class GetDatabaseHealthResponse
{
	public required bool IsHealthy { get; set; }
}