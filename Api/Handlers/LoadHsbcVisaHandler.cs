using Dapper;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Data.SqlClient;

namespace Api.Handlers;

public class LoadHsbcVisaHandler
{
	public ActionResult<LoadHsbcVisaResponse> Execute(LoadHsbcVisaRequest request)
	{
		using var connection = new SqlConnection(AppConfig.ConnectionString);
		connection.Open();

		int one = connection.Execute("delete from HsbcVisa");

		var fileNames = Directory.GetFiles(AppConfig.HsbcVisaDir, "*.csv").Order();
		foreach (string filename in fileNames)
		{
			var lines = File.ReadAllLines(filename);

			var records = lines
				.Where(line => !string.IsNullOrWhiteSpace(line))
				.Select(line =>
				{
					var words = line.Split(',');
					if (words[0].EndsWith(" 00:00:00"))
					{
						words[0] = words[0][..^9];
					}
					return new
						{
							Date = DateTime.ParseExact(words[0], "yyyy-MM-dd", null),
							Card = words[1],
							Merchant = words[2],
							Amount = decimal.Parse(words[3]),
							Shing = decimal.Parse(words[4]),
							Source = Path.GetFileName(filename)
						};
				});
				
			connection.Execute("insert into HsbcVisa (Date, Card, Merchant, Amount, Shing, Source) values (@Date, @Card, @Merchant, @Amount, @Shing, @Source)", records);
		}

		return new OkObjectResult(new LoadHsbcVisaResponse { Filenames = fileNames });
	}
}

public class LoadHsbcVisaRequest
{
}

public class LoadHsbcVisaResponse
{
	public required IEnumerable<string> Filenames { get; set; }
}