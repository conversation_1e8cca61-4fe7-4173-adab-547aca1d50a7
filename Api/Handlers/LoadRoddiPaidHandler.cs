using Dapper;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Data.SqlClient;

namespace Api.Handlers;

public class LoadRoddiPaidHandler
{
	public ActionResult<LoadRoddiPaidResponse> Execute(LoadRoddiPaidRequest request)
	{
		using var connection = new SqlConnection(AppConfig.ConnectionString);
		connection.Open();

		int deletedRows = connection.Execute("delete from RoddiPaid");

		var fileNames = Directory.GetFiles(AppConfig.RoddiPaidDir, "*.csv").Order();
		foreach (string filename in fileNames)
		{
			var lines = File.ReadAllLines(filename);

			var records = lines
				.Where(line => !string.IsNullOrWhiteSpace(line))
				.Select(line =>
				{
					var words = line.Split(',');
					return new
					{
						Date = DateTime.ParseExact(words[0], "yyyy-MM-dd", null),
						Amount = decimal.Parse(words[1]),
						Comment = words[2],
						Shing = decimal.Parse(words[3])
					};
				});
				
			connection.Execute("INSERT INTO RoddiPaid ([Date], Amount, Comment, Shing, InsertTime) VALUES (@Date, @Amount, @Comment, @Shing, getdate())", records);
		}

		return new OkObjectResult(new LoadRoddiPaidResponse { Filenames = fileNames });
	}
}

public class LoadRoddiPaidRequest
{
}

public class LoadRoddiPaidResponse
{
	public required IEnumerable<string> Filenames { get; set; }
}
