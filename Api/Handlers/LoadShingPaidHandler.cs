using Dapper;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Data.SqlClient;

namespace Api.Handlers;

public class LoadShingPaidHandler
{
	public ActionResult<LoadShingPaidResponse> Execute(LoadShingPaidRequest request)
	{
		using var connection = new SqlConnection(AppConfig.ConnectionString);
		connection.Open();

		int deletedRows = connection.Execute("delete from ShingPaid");

		var fileNames = Directory.GetFiles(AppConfig.ShingPaidDir, "*.csv").Order();
		foreach (string filename in fileNames)
		{
			var lines = File.ReadAllLines(filename);

			var records = lines
				.Where(line => !string.IsNullOrWhiteSpace(line))
				.Select(line =>
				{
					var words = line.Split(',');
					return new
					{
						Date = DateTime.ParseExact(words[0], "yyyy-MM-dd", null),
						Amount = decimal.Parse(words[1]),
						Comment = words[2],
						Shing = decimal.Parse(words[3])
					};
				});
				
			connection.Execute("INSERT INTO ShingPaid ([Date], Amount, Comment, Shing, InsertTime) VALUES (@Date, @Amount, @Comment, @Shing, getdate())", records);
		}

		return new OkObjectResult(new LoadShingPaidResponse { Filenames = fileNames });
	}
}

public class LoadShingPaidRequest
{
}

public class LoadShingPaidResponse
{
	public required IEnumerable<string> Filenames { get; set; }
}
