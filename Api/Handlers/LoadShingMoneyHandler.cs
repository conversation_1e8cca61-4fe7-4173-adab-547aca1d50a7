using Dapper;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Data.SqlClient;

namespace Api.Handlers;

public class LoadShingMoneyHandler
{
	public ActionResult<LoadShingMoneyResponse> Execute(LoadShingMoneyRequest request)
	{
		using var connection = new SqlConnection(AppConfig.ConnectionString);
		connection.Open();

		int deletedRows = connection.Execute("delete from ShingMoney");

		var fileNames = Directory.GetFiles(AppConfig.ShingMoneyDir, "*.csv").Order();
		foreach (string filename in fileNames)
		{
			var lines = File.ReadAllLines(filename);

			var records = lines
				.Where(line => !string.IsNullOrWhiteSpace(line))
				.Select(line =>
				{
					var words = line.Split(',');
					return new
					{
						Date = DateTime.ParseExact(words[0], "yyyy-MM-dd", null),
						Amount = decimal.Parse(words[1]),
						Comment = words[2]
					};
				});
				
			connection.Execute("INSERT INTO ShingMoney ([Date], Amount, Comment, InsertTime) VALUES (@Date, @Amount, @Comment, getdate())", records);
		}

		return new OkObjectResult(new LoadShingMoneyResponse { Filenames = fileNames });
	}
}

public class LoadShingMoneyRequest
{
}

public class LoadShingMoneyResponse
{
	public required IEnumerable<string> Filenames { get; set; }
}
