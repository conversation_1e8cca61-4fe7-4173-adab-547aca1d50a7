using Api.Handlers;
using Microsoft.AspNetCore.Mvc;

namespace Api.Controllers
{
	[ApiController]
	[Route("[controller]")]
	public class ShingMoneyController : ControllerBase
	{
		private readonly ILogger<ShingMoneyController> _logger;

		public ShingMoneyController(ILogger<ShingMoneyController> logger)
		{
			_logger = logger;
		}

		[HttpPost("load")]
		[ProducesResponseType(StatusCodes.Status200OK)]
		public ActionResult<LoadShingMoneyResponse> LoadShingMoney()
		{
			return (new LoadShingMoneyHandler()).Execute(new LoadShingMoneyRequest());
		}
	}
}
