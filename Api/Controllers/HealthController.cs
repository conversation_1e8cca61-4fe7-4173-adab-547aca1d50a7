using Api.Handlers;
using Microsoft.AspNetCore.Mvc;

namespace Api.Controllers
{
	[ApiController]
	[Route("[controller]")]
	public class HealthController : ControllerBase
	{
		private readonly ILogger<HealthController> _logger;

		public HealthController(ILogger<HealthController> logger)
		{
			_logger = logger;
		}

		[HttpGet(Name = "database")]
		[ProducesResponseType(StatusCodes.Status200OK)]
		public ActionResult<GetDatabaseHealthResponse> GetDatabaseHealth()
		{
			return (new GetDatabaseHealthHandler()).Execute(new GetDatabaseHealthRequest());
		}
	}
}
