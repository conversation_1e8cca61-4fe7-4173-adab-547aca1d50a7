using Api.Handlers;
using Microsoft.AspNetCore.Mvc;

namespace Api.Controllers
{
	[ApiController]
	[Route("[controller]")]
	public class HsbcAcctController : ControllerBase
	{
		private readonly ILogger<HsbcAcctController> _logger;

		public HsbcAcctController(ILogger<HsbcAcctController> logger)
		{
			_logger = logger;
		}

		[HttpPost("load")]
		[ProducesResponseType(StatusCodes.Status200OK)]
		public ActionResult<LoadHsbcAcctResponse> LoadHsbcAcct()
		{
			return (new LoadHsbcAcctHandler()).Execute(new LoadHsbcAcctRequest());
		}
	}
}
