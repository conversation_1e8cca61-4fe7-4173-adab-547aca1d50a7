using Api.Handlers;
using Microsoft.AspNetCore.Mvc;

namespace Api.Controllers
{
	[ApiController]
	[Route("[controller]")]
	public class HsbcVisaController : ControllerBase
	{
		private readonly ILogger<HsbcVisaController> _logger;

		public HsbcVisaController(ILogger<HsbcVisaController> logger)
		{
			_logger = logger;
		}

		[HttpPost("load")]
		[ProducesResponseType(StatusCodes.Status200OK)]
		public ActionResult<LoadHsbcVisaResponse> LoadHsbcVisa()
		{
			return (new LoadHsbcVisaHandler()).Execute(new LoadHsbcVisaRequest());
		}
	}
}
