using Api.Handlers;
using Microsoft.AspNetCore.Mvc;

namespace Api.Controllers
{
	[ApiController]
	[Route("[controller]")]
	public class RoddiPaidController : ControllerBase
	{
		private readonly ILogger<RoddiPaidController> _logger;

		public RoddiPaidController(ILogger<RoddiPaidController> logger)
		{
			_logger = logger;
		}

		[HttpPost("load")]
		[ProducesResponseType(StatusCodes.Status200OK)]
		public ActionResult<LoadRoddiPaidResponse> LoadRoddiPaid()
		{
			return (new LoadRoddiPaidHandler()).Execute(new LoadRoddiPaidRequest());
		}
	}
}
