namespace Api;

public static class AppConfig
{
	public const string HsbcAcctDir = "/home/<USER>/Code/ShingMoney/HsbcAcct";
	public const string HsbcVisaDir = "/home/<USER>/Code/ShingMoney/HsbcVisa";
	public const string ShingMoneyDir = "/home/<USER>/Code/ShingMoney/ShingMoney";
	public const string RoddiPaidDir = "/home/<USER>/Code/ShingMoney/RoddiPaid";
	public const string ShingPaidDir = "/home/<USER>/Code/ShingMoney/ShingPaid";

	public static string? ConnectionString => File.ReadAllText("/home/<USER>/Code/ShingMoneyConnectionString.txt");
}